# Core libraries
import os
import json
import numpy as np
import pandas as pd
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Image processing
import cv2
from PIL import Image, ImageEnhance, ImageFilter
import matplotlib.pyplot as plt
import matplotlib.patches as patches

# PDF processing
import fitz  # PyMuPDF
import pdfplumber

# OCR libraries
import pytesseract
import easyocr

# Text processing
import re
from difflib import SequenceMatcher
from collections import defaultdict

# Visualization
import seaborn as sns
plt.style.use('default')

print("All libraries imported successfully!")

# File paths
TEMPLATE_PDF = "template.pdf"
SAMPLE_IMAGE = "sample.jpg"

# OCR Configuration
TESSERACT_CONFIG = '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz .,()-/'

# Image processing parameters
IMAGE_PROCESSING_PARAMS = {
    'dpi': 300,
    'contrast_factor': 1.2,
    'brightness_factor': 1.1,
    'blur_radius': 0.5,
    'threshold_value': 127
}

# Field detection parameters
FIELD_DETECTION_PARAMS = {
    'min_confidence': 30,
    'similarity_threshold': 0.6,
    'proximity_threshold': 50  # pixels
}

print(f"Template PDF: {TEMPLATE_PDF}")
print(f"Sample Image: {SAMPLE_IMAGE}")
print(f"Files exist: PDF={os.path.exists(TEMPLATE_PDF)}, Image={os.path.exists(SAMPLE_IMAGE)}")

class TemplateAnalyzer:
    """Analyzes PDF template to extract form structure and field locations."""
    
    def __init__(self, pdf_path):
        self.pdf_path = pdf_path
        self.template_data = {}
        self.form_fields = []
        self.text_elements = []
        
    def analyze_template(self):
        """Main method to analyze the PDF template."""
        try:
            # Method 1: Try PyMuPDF for form fields
            self._extract_form_fields_pymupdf()
            
            # Method 2: Try pdfplumber for text extraction
            self._extract_text_elements_pdfplumber()
            
            # Method 3: Convert to image and analyze visually
            self._convert_to_image_analysis()
            
            print(f"Template analysis complete:")
            print(f"- Form fields found: {len(self.form_fields)}")
            print(f"- Text elements found: {len(self.text_elements)}")
            
            return self.template_data
            
        except Exception as e:
            print(f"Error analyzing template: {str(e)}")
            return {}
    
    def _extract_form_fields_pymupdf(self):
        """Extract interactive form fields using PyMuPDF."""
        try:
            doc = fitz.open(self.pdf_path)
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                
                # Get form fields
                fields = page.get_form_fields()
                if fields:
                    for field in fields:
                        field_info = {
                            'page': page_num,
                            'name': field.get('field_name', ''),
                            'type': field.get('field_type', ''),
                            'rect': field.get('field_rect', []),
                            'value': field.get('field_value', '')
                        }
                        self.form_fields.append(field_info)
                
                # Get text annotations and positions
                text_dict = page.get_text("dict")
                for block in text_dict["blocks"]:
                    if "lines" in block:
                        for line in block["lines"]:
                            for span in line["spans"]:
                                text_info = {
                                    'page': page_num,
                                    'text': span["text"].strip(),
                                    'bbox': span["bbox"],
                                    'font': span["font"],
                                    'size': span["size"]
                                }
                                if text_info['text']:  # Only add non-empty text
                                    self.text_elements.append(text_info)
            
            doc.close()
            
        except Exception as e:
            print(f"PyMuPDF extraction failed: {str(e)}")
    
    def _extract_text_elements_pdfplumber(self):
        """Extract text elements using pdfplumber."""
        try:
            with pdfplumber.open(self.pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    # Extract text with positions
                    chars = page.chars
                    
                    # Group characters into words
                    words = page.extract_words()
                    
                    for word in words:
                        text_info = {
                            'page': page_num,
                            'text': word['text'],
                            'bbox': [word['x0'], word['top'], word['x1'], word['bottom']],
                            'font': word.get('fontname', ''),
                            'size': word.get('size', 0)
                        }
                        # Avoid duplicates from PyMuPDF
                        if not any(t['text'] == text_info['text'] and 
                                 abs(t['bbox'][0] - text_info['bbox'][0]) < 5 
                                 for t in self.text_elements):
                            self.text_elements.append(text_info)
                            
        except Exception as e:
            print(f"pdfplumber extraction failed: {str(e)}")
    
    def _convert_to_image_analysis(self):
        """Convert PDF to image for visual analysis."""
        try:
            doc = fitz.open(self.pdf_path)
            page = doc.load_page(0)  # First page
            
            # Convert to image
            mat = fitz.Matrix(2, 2)  # 2x zoom
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("ppm")
            
            # Save template image for reference
            with open("template_image.ppm", "wb") as f:
                f.write(img_data)
            
            # Convert to PIL Image for processing
            template_img = Image.open("template_image.ppm")
            
            # Store template data
            self.template_data = {
                'form_fields': self.form_fields,
                'text_elements': self.text_elements,
                'template_image': template_img,
                'page_size': [page.rect.width, page.rect.height]
            }
            
            doc.close()
            
        except Exception as e:
            print(f"Image conversion failed: {str(e)}")
    
    def get_field_labels(self):
        """Extract potential field labels from text elements."""
        labels = []
        
        # Common field label patterns
        label_patterns = [
            r'.*name.*:?',
            r'.*address.*:?',
            r'.*phone.*:?',
            r'.*email.*:?',
            r'.*date.*:?',
            r'.*signature.*:?',
            r'.*[a-zA-Z]+.*:$',  # Any text ending with colon
        ]
        
        for text_elem in self.text_elements:
            text = text_elem['text'].lower().strip()
            for pattern in label_patterns:
                if re.match(pattern, text, re.IGNORECASE):
                    labels.append({
                        'label': text_elem['text'],
                        'bbox': text_elem['bbox'],
                        'page': text_elem['page']
                    })
                    break
        
        return labels

print("TemplateAnalyzer class defined successfully!")

class ImageProcessor:
    """Handles image preprocessing and OCR for scanned forms."""
    
    def __init__(self, image_path, processing_params=None):
        self.image_path = image_path
        self.params = processing_params or IMAGE_PROCESSING_PARAMS
        self.original_image = None
        self.processed_image = None
        self.ocr_results = {}
        
    def load_and_preprocess(self):
        """Load and preprocess the image for better OCR results."""
        try:
            # Load image
            self.original_image = Image.open(self.image_path)
            
            # Convert to RGB if necessary
            if self.original_image.mode != 'RGB':
                self.original_image = self.original_image.convert('RGB')
            
            # Apply preprocessing steps
            processed = self.original_image.copy()
            
            # Enhance contrast
            enhancer = ImageEnhance.Contrast(processed)
            processed = enhancer.enhance(self.params['contrast_factor'])
            
            # Enhance brightness
            enhancer = ImageEnhance.Brightness(processed)
            processed = enhancer.enhance(self.params['brightness_factor'])
            
            # Apply slight blur to reduce noise
            processed = processed.filter(ImageFilter.GaussianBlur(radius=self.params['blur_radius']))
            
            # Convert to grayscale for better OCR
            processed = processed.convert('L')
            
            # Apply threshold
            processed = processed.point(lambda x: 0 if x < self.params['threshold_value'] else 255, '1')
            
            self.processed_image = processed
            
            print(f"Image preprocessing complete. Size: {self.processed_image.size}")
            return True
            
        except Exception as e:
            print(f"Error preprocessing image: {str(e)}")
            return False
    
    def perform_ocr(self, use_both_engines=True):
        """Perform OCR using Tesseract and optionally EasyOCR."""
        if self.processed_image is None:
            print("No processed image available. Run load_and_preprocess() first.")
            return {}
        
        results = {}
        
        # Tesseract OCR
        try:
            # Get detailed OCR data
            tesseract_data = pytesseract.image_to_data(
                self.processed_image, 
                config=TESSERACT_CONFIG,
                output_type=pytesseract.Output.DICT
            )
            
            # Process Tesseract results
            tesseract_words = []
            for i in range(len(tesseract_data['text'])):
                if int(tesseract_data['conf'][i]) > FIELD_DETECTION_PARAMS['min_confidence']:
                    word_info = {
                        'text': tesseract_data['text'][i].strip(),
                        'confidence': int(tesseract_data['conf'][i]),
                        'bbox': [
                            tesseract_data['left'][i],
                            tesseract_data['top'][i],
                            tesseract_data['left'][i] + tesseract_data['width'][i],
                            tesseract_data['top'][i] + tesseract_data['height'][i]
                        ],
                        'engine': 'tesseract'
                    }
                    if word_info['text']:  # Only add non-empty text
                        tesseract_words.append(word_info)
            
            results['tesseract'] = tesseract_words
            print(f"Tesseract OCR complete: {len(tesseract_words)} words detected")
            
        except Exception as e:
            print(f"Tesseract OCR failed: {str(e)}")
            results['tesseract'] = []
        
        # EasyOCR (if requested)
        if use_both_engines:
            try:
                reader = easyocr.Reader(['en'])
                
                # Convert PIL image to numpy array
                img_array = np.array(self.processed_image)
                
                # Perform OCR
                easyocr_results = reader.readtext(img_array)
                
                # Process EasyOCR results
                easyocr_words = []
                for (bbox, text, confidence) in easyocr_results:
                    if confidence > FIELD_DETECTION_PARAMS['min_confidence'] / 100:  # EasyOCR uses 0-1 scale
                        # Convert bbox format
                        x_coords = [point[0] for point in bbox]
                        y_coords = [point[1] for point in bbox]
                        
                        word_info = {
                            'text': text.strip(),
                            'confidence': int(confidence * 100),
                            'bbox': [min(x_coords), min(y_coords), max(x_coords), max(y_coords)],
                            'engine': 'easyocr'
                        }
                        if word_info['text']:  # Only add non-empty text
                            easyocr_words.append(word_info)
                
                results['easyocr'] = easyocr_words
                print(f"EasyOCR complete: {len(easyocr_words)} words detected")
                
            except Exception as e:
                print(f"EasyOCR failed: {str(e)}")
                results['easyocr'] = []
        
        self.ocr_results = results
        return results
    
    def combine_ocr_results(self):
        """Combine results from multiple OCR engines."""
        if not self.ocr_results:
            return []
        
        combined_words = []
        
        # Start with Tesseract results
        if 'tesseract' in self.ocr_results:
            combined_words.extend(self.ocr_results['tesseract'])
        
        # Add EasyOCR results that don't overlap significantly
        if 'easyocr' in self.ocr_results:
            for easyocr_word in self.ocr_results['easyocr']:
                # Check for overlap with existing words
                overlap_found = False
                for existing_word in combined_words:
                    if self._boxes_overlap(easyocr_word['bbox'], existing_word['bbox']):
                        # Keep the word with higher confidence
                        if easyocr_word['confidence'] > existing_word['confidence']:
                            combined_words.remove(existing_word)
                            combined_words.append(easyocr_word)
                        overlap_found = True
                        break
                
                if not overlap_found:
                    combined_words.append(easyocr_word)
        
        # Sort by position (top to bottom, left to right)
        combined_words.sort(key=lambda x: (x['bbox'][1], x['bbox'][0]))
        
        print(f"Combined OCR results: {len(combined_words)} words total")
        return combined_words
    
    def _boxes_overlap(self, box1, box2, threshold=0.5):
        """Check if two bounding boxes overlap significantly."""
        x1_min, y1_min, x1_max, y1_max = box1
        x2_min, y2_min, x2_max, y2_max = box2
        
        # Calculate intersection
        x_overlap = max(0, min(x1_max, x2_max) - max(x1_min, x2_min))
        y_overlap = max(0, min(y1_max, y2_max) - max(y1_min, y2_min))
        
        if x_overlap == 0 or y_overlap == 0:
            return False
        
        intersection_area = x_overlap * y_overlap
        
        # Calculate areas
        area1 = (x1_max - x1_min) * (y1_max - y1_min)
        area2 = (x2_max - x2_min) * (y2_max - y2_min)
        
        # Calculate overlap ratio
        overlap_ratio = intersection_area / min(area1, area2)
        
        return overlap_ratio > threshold
    
    def visualize_results(self, ocr_words=None, save_path=None):
        """Visualize OCR results on the image."""
        if self.original_image is None:
            print("No image loaded")
            return
        
        if ocr_words is None:
            ocr_words = self.combine_ocr_results()
        
        # Create figure
        fig, ax = plt.subplots(1, 1, figsize=(15, 10))
        ax.imshow(self.original_image)
        
        # Draw bounding boxes and text
        for word in ocr_words:
            bbox = word['bbox']
            rect = patches.Rectangle(
                (bbox[0], bbox[1]), 
                bbox[2] - bbox[0], 
                bbox[3] - bbox[1],
                linewidth=1, 
                edgecolor='red' if word['engine'] == 'tesseract' else 'blue',
                facecolor='none'
            )
            ax.add_patch(rect)
            
            # Add text label
            ax.text(
                bbox[0], bbox[1] - 5, 
                f"{word['text']} ({word['confidence']}%)",
                fontsize=8, 
                color='red' if word['engine'] == 'tesseract' else 'blue',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.7)
            )
        
        ax.set_title(f"OCR Results - {len(ocr_words)} words detected")
        ax.axis('off')
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
        
        plt.show()

print("ImageProcessor class defined successfully!")

class FormFieldMapper:
    """Maps OCR results to form fields using template analysis."""
    
    def __init__(self, template_data, ocr_words):
        self.template_data = template_data
        self.ocr_words = ocr_words
        self.field_mappings = {}
        self.extracted_data = {}
        
    def map_fields(self):
        """Main method to map OCR results to form fields."""
        try:
            # Get field labels from template
            field_labels = self._identify_field_labels()
            
            # Map OCR words to fields
            self.field_mappings = self._map_ocr_to_fields(field_labels)
            
            # Extract structured data
            self.extracted_data = self._extract_structured_data()
            
            print(f"Field mapping complete:")
            print(f"- Fields identified: {len(field_labels)}")
            print(f"- Mappings created: {len(self.field_mappings)}")
            print(f"- Data extracted: {len(self.extracted_data)}")
            
            return self.extracted_data
            
        except Exception as e:
            print(f"Error in field mapping: {str(e)}")
            return {}
    
    def _identify_field_labels(self):
        """Identify field labels from template text elements."""
        field_labels = []
        
        # Common field patterns
        field_patterns = {
            'name': [r'.*name.*', r'.*full.*name.*', r'.*first.*name.*', r'.*last.*name.*'],
            'address': [r'.*address.*', r'.*street.*', r'.*city.*', r'.*state.*', r'.*zip.*'],
            'phone': [r'.*phone.*', r'.*tel.*', r'.*mobile.*', r'.*contact.*'],
            'email': [r'.*email.*', r'.*e-mail.*', r'.*mail.*'],
            'date': [r'.*date.*', r'.*birth.*', r'.*dob.*'],
            'signature': [r'.*signature.*', r'.*sign.*'],
            'id': [r'.*id.*', r'.*number.*', r'.*ssn.*', r'.*social.*'],
            'gender': [r'.*gender.*', r'.*sex.*', r'.*male.*', r'.*female.*'],
            'occupation': [r'.*occupation.*', r'.*job.*', r'.*work.*', r'.*profession.*']
        }
        
        # Check template text elements
        if 'text_elements' in self.template_data:
            for text_elem in self.template_data['text_elements']:
                text = text_elem['text'].lower().strip()
                
                # Skip very short text
                if len(text) < 2:
                    continue
                
                # Check against patterns
                for field_type, patterns in field_patterns.items():
                    for pattern in patterns:
                        if re.search(pattern, text, re.IGNORECASE):
                            field_labels.append({
                                'type': field_type,
                                'label': text_elem['text'],
                                'bbox': text_elem['bbox'],
                                'page': text_elem.get('page', 0)
                            })
                            break
                    else:
                        continue
                    break
        
        # Also check for text ending with colon (likely labels)
        if 'text_elements' in self.template_data:
            for text_elem in self.template_data['text_elements']:
                text = text_elem['text'].strip()
                if text.endswith(':') and len(text) > 2:
                    # Try to infer field type from label
                    field_type = self._infer_field_type(text)
                    
                    # Avoid duplicates
                    if not any(label['label'] == text for label in field_labels):
                        field_labels.append({
                            'type': field_type,
                            'label': text,
                            'bbox': text_elem['bbox'],
                            'page': text_elem.get('page', 0)
                        })
        
        return field_labels
    
    def _infer_field_type(self, label_text):
        """Infer field type from label text."""
        text = label_text.lower()
        
        if any(word in text for word in ['name', 'first', 'last']):
            return 'name'
        elif any(word in text for word in ['address', 'street', 'city', 'state', 'zip']):
            return 'address'
        elif any(word in text for word in ['phone', 'tel', 'mobile']):
            return 'phone'
        elif any(word in text for word in ['email', 'mail']):
            return 'email'
        elif any(word in text for word in ['date', 'birth', 'dob']):
            return 'date'
        elif any(word in text for word in ['signature', 'sign']):
            return 'signature'
        elif any(word in text for word in ['id', 'number', 'ssn']):
            return 'id'
        elif any(word in text for word in ['gender', 'sex']):
            return 'gender'
        elif any(word in text for word in ['occupation', 'job', 'work']):
            return 'occupation'
        else:
            return 'other'
    
    def _map_ocr_to_fields(self, field_labels):
        """Map OCR words to identified field labels."""
        mappings = {}
        
        for field_label in field_labels:
            # Find OCR words near this field label
            nearby_words = self._find_nearby_words(
                field_label['bbox'], 
                self.ocr_words,
                proximity_threshold=FIELD_DETECTION_PARAMS['proximity_threshold']
            )
            
            if nearby_words:
                # Combine nearby words into field value
                field_value = self._combine_words_to_value(nearby_words)
                
                mappings[field_label['type']] = {
                    'label': field_label['label'],
                    'value': field_value,
                    'confidence': self._calculate_field_confidence(nearby_words),
                    'words': nearby_words,
                    'label_bbox': field_label['bbox']
                }
        
        return mappings
    
    def _find_nearby_words(self, label_bbox, ocr_words, proximity_threshold=50):
        """Find OCR words near a field label."""
        nearby_words = []
        
        label_x, label_y, label_x2, label_y2 = label_bbox
        label_center_x = (label_x + label_x2) / 2
        label_center_y = (label_y + label_y2) / 2
        
        for word in ocr_words:
            word_x, word_y, word_x2, word_y2 = word['bbox']
            word_center_x = (word_x + word_x2) / 2
            word_center_y = (word_y + word_y2) / 2
            
            # Calculate distance
            distance = np.sqrt(
                (word_center_x - label_center_x) ** 2 + 
                (word_center_y - label_center_y) ** 2
            )
            
            # Check if word is nearby and not the label itself
            if (distance <= proximity_threshold and 
                not self._is_similar_text(word['text'], self._extract_label_text(label_bbox))):
                
                nearby_words.append({
                    'word': word,
                    'distance': distance
                })
        
        # Sort by distance
        nearby_words.sort(key=lambda x: x['distance'])
        
        return [item['word'] for item in nearby_words]
    
    def _extract_label_text(self, label_bbox):
        """Extract text from template that matches the label bbox."""
        if 'text_elements' not in self.template_data:
            return ""
        
        for text_elem in self.template_data['text_elements']:
            if self._boxes_similar(text_elem['bbox'], label_bbox):
                return text_elem['text']
        
        return ""
    
    def _boxes_similar(self, box1, box2, threshold=10):
        """Check if two bounding boxes are similar (within threshold)."""
        return (abs(box1[0] - box2[0]) <= threshold and
                abs(box1[1] - box2[1]) <= threshold and
                abs(box1[2] - box2[2]) <= threshold and
                abs(box1[3] - box2[3]) <= threshold)
    
    def _is_similar_text(self, text1, text2, threshold=0.8):
        """Check if two text strings are similar."""
        if not text1 or not text2:
            return False
        
        similarity = SequenceMatcher(None, text1.lower(), text2.lower()).ratio()
        return similarity >= threshold
    
    def _combine_words_to_value(self, words):
        """Combine multiple OCR words into a single field value."""
        if not words:
            return ""
        
        # Sort words by position (left to right, top to bottom)
        sorted_words = sorted(words, key=lambda w: (w['bbox'][1], w['bbox'][0]))
        
        # Combine text
        combined_text = " ".join(word['text'] for word in sorted_words)
        
        return combined_text.strip()
    
    def _calculate_field_confidence(self, words):
        """Calculate confidence score for a field based on its words."""
        if not words:
            return 0
        
        # Average confidence of all words
        total_confidence = sum(word['confidence'] for word in words)
        return total_confidence / len(words)
    
    def _extract_structured_data(self):
        """Extract structured data from field mappings."""
        structured_data = {}
        
        for field_type, mapping in self.field_mappings.items():
            # Clean and validate field value
            cleaned_value = self._clean_field_value(mapping['value'], field_type)
            
            structured_data[field_type] = {
                'value': cleaned_value,
                'confidence': mapping['confidence'],
                'label': mapping['label'],
                'raw_value': mapping['value']
            }
        
        return structured_data
    
    def _clean_field_value(self, value, field_type):
        """Clean and validate field values based on field type."""
        if not value:
            return ""
        
        cleaned = value.strip()
        
        # Type-specific cleaning
        if field_type == 'phone':
            # Extract digits and common phone characters
            cleaned = re.sub(r'[^0-9\-\(\)\+\s]', '', cleaned)
            cleaned = re.sub(r'\s+', ' ', cleaned).strip()
            
        elif field_type == 'email':
            # Basic email validation
            email_match = re.search(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', cleaned)
            cleaned = email_match.group(0) if email_match else cleaned
            
        elif field_type == 'date':
            # Try to standardize date format
            date_patterns = [
                r'\d{1,2}/\d{1,2}/\d{4}',
                r'\d{1,2}-\d{1,2}-\d{4}',
                r'\d{4}-\d{1,2}-\d{1,2}',
                r'\d{1,2}\s+\w+\s+\d{4}'
            ]
            
            for pattern in date_patterns:
                date_match = re.search(pattern, cleaned)
                if date_match:
                    cleaned = date_match.group(0)
                    break
                    
        elif field_type == 'name':
            # Capitalize names properly
            cleaned = ' '.join(word.capitalize() for word in cleaned.split())
            
        elif field_type == 'id':
            # Remove non-alphanumeric characters
            cleaned = re.sub(r'[^a-zA-Z0-9]', '', cleaned)
        
        return cleaned

print("FormFieldMapper class defined successfully!")

class DataValidator:
    """Validates and formats extracted form data."""
    
    def __init__(self):
        self.validation_rules = {
            'name': {'min_length': 2, 'max_length': 100, 'pattern': r'^[a-zA-Z\s\-\.]+$'},
            'email': {'pattern': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'},
            'phone': {'min_length': 10, 'max_length': 15, 'pattern': r'^[\d\-\(\)\+\s]+$'},
            'date': {'pattern': r'^\d{1,2}[/-]\d{1,2}[/-]\d{4}$'},
            'id': {'min_length': 5, 'max_length': 20, 'pattern': r'^[a-zA-Z0-9]+$'}
        }
    
    def validate_data(self, extracted_data):
        """Validate extracted data against rules."""
        validation_results = {}
        
        for field_type, field_data in extracted_data.items():
            value = field_data['value']
            confidence = field_data['confidence']
            
            validation_result = {
                'value': value,
                'confidence': confidence,
                'is_valid': True,
                'validation_errors': [],
                'quality_score': confidence
            }
            
            # Apply validation rules if they exist for this field type
            if field_type in self.validation_rules:
                rules = self.validation_rules[field_type]
                
                # Check minimum length
                if 'min_length' in rules and len(value) < rules['min_length']:
                    validation_result['is_valid'] = False
                    validation_result['validation_errors'].append(f"Value too short (min: {rules['min_length']})")
                
                # Check maximum length
                if 'max_length' in rules and len(value) > rules['max_length']:
                    validation_result['is_valid'] = False
                    validation_result['validation_errors'].append(f"Value too long (max: {rules['max_length']})")
                
                # Check pattern
                if 'pattern' in rules and value and not re.match(rules['pattern'], value):
                    validation_result['is_valid'] = False
                    validation_result['validation_errors'].append("Value doesn't match expected format")
            
            # Calculate quality score
            quality_score = confidence
            if not validation_result['is_valid']:
                quality_score *= 0.5  # Reduce score for invalid data
            
            validation_result['quality_score'] = quality_score
            validation_results[field_type] = validation_result
        
        return validation_results
    
    def generate_summary_report(self, validation_results):
        """Generate a summary report of the validation results."""
        total_fields = len(validation_results)
        valid_fields = sum(1 for result in validation_results.values() if result['is_valid'])
        avg_confidence = np.mean([result['confidence'] for result in validation_results.values()])
        avg_quality = np.mean([result['quality_score'] for result in validation_results.values()])
        
        report = {
            'total_fields': total_fields,
            'valid_fields': valid_fields,
            'invalid_fields': total_fields - valid_fields,
            'validation_rate': valid_fields / total_fields if total_fields > 0 else 0,
            'average_confidence': avg_confidence,
            'average_quality_score': avg_quality,
            'field_details': validation_results
        }
        
        return report

print("DataValidator class defined successfully!")

def run_form_extraction_pipeline(template_path, image_path, output_format='both'):
    """
    Main pipeline function to extract data from scanned forms.
    
    Args:
        template_path (str): Path to the PDF template
        image_path (str): Path to the scanned form image
        output_format (str): 'json', 'dataframe', or 'both'
    
    Returns:
        dict: Results containing extracted data and metadata
    """
    
    print("=" * 60)
    print("FORM DATA EXTRACTION PIPELINE")
    print("=" * 60)
    
    results = {
        'success': False,
        'extracted_data': {},
        'validation_report': {},
        'metadata': {},
        'errors': []
    }
    
    try:
        # Step 1: Analyze Template
        print("\n1. Analyzing PDF Template...")
        template_analyzer = TemplateAnalyzer(template_path)
        template_data = template_analyzer.analyze_template()
        
        if not template_data:
            results['errors'].append("Failed to analyze template")
            return results
        
        # Step 2: Process Image and Perform OCR
        print("\n2. Processing Image and Performing OCR...")
        image_processor = ImageProcessor(image_path)
        
        if not image_processor.load_and_preprocess():
            results['errors'].append("Failed to preprocess image")
            return results
        
        ocr_results = image_processor.perform_ocr(use_both_engines=True)
        combined_words = image_processor.combine_ocr_results()
        
        if not combined_words:
            results['errors'].append("No text detected in image")
            return results
        
        # Step 3: Map Fields and Extract Data
        print("\n3. Mapping Fields and Extracting Data...")
        field_mapper = FormFieldMapper(template_data, combined_words)
        extracted_data = field_mapper.map_fields()
        
        if not extracted_data:
            results['errors'].append("No fields could be mapped")
            return results
        
        # Step 4: Validate Data
        print("\n4. Validating Extracted Data...")
        validator = DataValidator()
        validation_results = validator.validate_data(extracted_data)
        validation_report = validator.generate_summary_report(validation_results)
        
        # Step 5: Generate Output
        print("\n5. Generating Output...")
        
        # Prepare results
        results['success'] = True
        results['extracted_data'] = extracted_data
        results['validation_report'] = validation_report
        results['metadata'] = {
            'template_file': template_path,
            'image_file': image_path,
            'processing_timestamp': pd.Timestamp.now().isoformat(),
            'total_ocr_words': len(combined_words),
            'template_text_elements': len(template_data.get('text_elements', [])),
            'template_form_fields': len(template_data.get('form_fields', []))
        }
        
        # Generate different output formats
        if output_format in ['json', 'both']:
            json_output = generate_json_output(results)
            results['json_output'] = json_output
        
        if output_format in ['dataframe', 'both']:
            df_output = generate_dataframe_output(extracted_data, validation_results)
            results['dataframe_output'] = df_output
        
        # Visualize results
        print("\n6. Visualizing Results...")
        image_processor.visualize_results(combined_words, save_path='ocr_results.png')
        
        print("\n" + "=" * 60)
        print("EXTRACTION COMPLETE!")
        print("=" * 60)
        
        return results
        
    except Exception as e:
        error_msg = f"Pipeline error: {str(e)}"
        print(f"\nERROR: {error_msg}")
        results['errors'].append(error_msg)
        return results

def generate_json_output(results):
    """Generate JSON output format."""
    json_data = {
        'extraction_metadata': results['metadata'],
        'validation_summary': {
            'total_fields': results['validation_report']['total_fields'],
            'valid_fields': results['validation_report']['valid_fields'],
            'validation_rate': results['validation_report']['validation_rate'],
            'average_confidence': results['validation_report']['average_confidence']
        },
        'extracted_fields': {}
    }
    
    # Add field data
    for field_type, field_data in results['extracted_data'].items():
        validation_data = results['validation_report']['field_details'][field_type]
        
        json_data['extracted_fields'][field_type] = {
            'value': field_data['value'],
            'confidence': field_data['confidence'],
            'is_valid': validation_data['is_valid'],
            'quality_score': validation_data['quality_score'],
            'label': field_data['label']
        }
    
    return json_data

def generate_dataframe_output(extracted_data, validation_results):
    """Generate pandas DataFrame output format."""
    df_data = []
    
    for field_type, field_data in extracted_data.items():
        validation_data = validation_results[field_type]
        
        df_data.append({
            'field_type': field_type,
            'field_label': field_data['label'],
            'extracted_value': field_data['value'],
            'confidence': field_data['confidence'],
            'is_valid': validation_data['is_valid'],
            'quality_score': validation_data['quality_score'],
            'validation_errors': '; '.join(validation_data['validation_errors']) if validation_data['validation_errors'] else 'None'
        })
    
    df = pd.DataFrame(df_data)
    return df

print("Main pipeline functions defined successfully!")

# Run the complete form extraction pipeline
results = run_form_extraction_pipeline(
    template_path=TEMPLATE_PDF,
    image_path=SAMPLE_IMAGE,
    output_format='both'
)

# Display results
if results['success']:
    print("\n📊 EXTRACTION RESULTS SUMMARY:")
    print("-" * 40)
    
    validation_report = results['validation_report']
    print(f"Total fields extracted: {validation_report['total_fields']}")
    print(f"Valid fields: {validation_report['valid_fields']}")
    print(f"Validation rate: {validation_report['validation_rate']:.1%}")
    print(f"Average confidence: {validation_report['average_confidence']:.1f}%")
    print(f"Average quality score: {validation_report['average_quality_score']:.1f}%")
    
else:
    print("\n❌ EXTRACTION FAILED:")
    for error in results['errors']:
        print(f"- {error}")

if results['success']:
    print("\n📋 EXTRACTED FIELD DATA:")
    print("=" * 50)
    
    for field_type, field_data in results['extracted_data'].items():
        validation_info = results['validation_report']['field_details'][field_type]
        
        status_icon = "✅" if validation_info['is_valid'] else "❌"
        
        print(f"\n{status_icon} {field_type.upper()}:")
        print(f"   Label: {field_data['label']}")
        print(f"   Value: '{field_data['value']}'")
        print(f"   Confidence: {field_data['confidence']:.1f}%")
        print(f"   Quality Score: {validation_info['quality_score']:.1f}%")
        
        if validation_info['validation_errors']:
            print(f"   Validation Errors: {', '.join(validation_info['validation_errors'])}")
    
    print("\n" + "=" * 50)

if results['success'] and 'dataframe_output' in results:
    print("\n📊 RESULTS AS DATAFRAME:")
    df = results['dataframe_output']
    
    # Display with nice formatting
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', None)
    pd.set_option('display.max_colwidth', 50)
    
    display(df)
    
    # Save to CSV
    csv_filename = 'extracted_form_data.csv'
    df.to_csv(csv_filename, index=False)
    print(f"\n💾 DataFrame saved to: {csv_filename}")

if results['success'] and 'json_output' in results:
    print("\n📄 RESULTS AS JSON:")
    
    # Pretty print JSON
    json_str = json.dumps(results['json_output'], indent=2, ensure_ascii=False)
    print(json_str)
    
    # Save to file
    json_filename = 'extracted_form_data.json'
    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump(results['json_output'], f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 JSON saved to: {json_filename}")

if results['success']:
    # Create visualization of confidence scores
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Confidence scores by field
    field_types = list(results['extracted_data'].keys())
    confidences = [results['extracted_data'][ft]['confidence'] for ft in field_types]
    quality_scores = [results['validation_report']['field_details'][ft]['quality_score'] for ft in field_types]
    
    x_pos = np.arange(len(field_types))
    
    ax1.bar(x_pos - 0.2, confidences, 0.4, label='OCR Confidence', alpha=0.8, color='skyblue')
    ax1.bar(x_pos + 0.2, quality_scores, 0.4, label='Quality Score', alpha=0.8, color='lightcoral')
    
    ax1.set_xlabel('Field Types')
    ax1.set_ylabel('Score (%)')
    ax1.set_title('Field Confidence and Quality Scores')
    ax1.set_xticks(x_pos)
    ax1.set_xticklabels(field_types, rotation=45, ha='right')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Validation status pie chart
    valid_count = results['validation_report']['valid_fields']
    invalid_count = results['validation_report']['invalid_fields']
    
    if invalid_count > 0:
        labels = ['Valid Fields', 'Invalid Fields']
        sizes = [valid_count, invalid_count]
        colors = ['lightgreen', 'lightcoral']
    else:
        labels = ['Valid Fields']
        sizes = [valid_count]
        colors = ['lightgreen']
    
    ax2.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    ax2.set_title('Field Validation Status')
    
    plt.tight_layout()
    plt.savefig('extraction_analysis.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("\n📈 Analysis charts saved as 'extraction_analysis.png'")

def troubleshoot_extraction_issues(results):
    """Provide troubleshooting suggestions based on results."""
    
    print("\n🔧 TROUBLESHOOTING SUGGESTIONS:")
    print("=" * 40)
    
    if not results['success']:
        print("\n❌ Extraction failed. Common issues and solutions:")
        
        for error in results['errors']:
            print(f"\nError: {error}")
            
            if "template" in error.lower():
                print("Solutions:")
                print("- Ensure the PDF template file exists and is readable")
                print("- Try a different PDF processing library")
                print("- Check if the PDF is password protected")
                
            elif "image" in error.lower():
                print("Solutions:")
                print("- Ensure the image file exists and is in a supported format")
                print("- Try preprocessing the image (adjust contrast, resolution)")
                print("- Check image quality and scan resolution")
                
            elif "ocr" in error.lower() or "text" in error.lower():
                print("Solutions:")
                print("- Improve image quality (higher resolution, better contrast)")
                print("- Try different OCR engines or settings")
                print("- Ensure text is clearly visible and not handwritten")
                
            elif "field" in error.lower():
                print("Solutions:")
                print("- Check if template and filled form have similar layouts")
                print("- Adjust proximity thresholds for field detection")
                print("- Manually verify field label patterns")
    
    else:
        validation_report = results['validation_report']
        
        if validation_report['validation_rate'] < 0.8:
            print("\n⚠️ Low validation rate detected. Suggestions:")
            print("- Review validation rules for field types")
            print("- Check OCR accuracy for problematic fields")
            print("- Consider manual verification of extracted data")
        
        if validation_report['average_confidence'] < 70:
            print("\n⚠️ Low OCR confidence detected. Suggestions:")
            print("- Improve image preprocessing (contrast, brightness)")
            print("- Use higher resolution scans")
            print("- Try different OCR engines or configurations")
        
        if validation_report['total_fields'] < 3:
            print("\n⚠️ Few fields detected. Suggestions:")
            print("- Check template analysis results")
            print("- Verify field label patterns")
            print("- Adjust field detection parameters")
        
        if validation_report['validation_rate'] >= 0.8 and validation_report['average_confidence'] >= 70:
            print("\n✅ Extraction appears successful!")
            print("- Results look good with high confidence and validation rates")
            print("- Consider spot-checking a few fields for accuracy")

# Run troubleshooting analysis
troubleshoot_extraction_issues(results)

print("\n" + "=" * 60)
print("FORM EXTRACTION PIPELINE SUMMARY")
print("=" * 60)

print("\n📋 What this solution provides:")
print("✅ PDF template analysis and form structure detection")
print("✅ Advanced image preprocessing for better OCR results")
print("✅ Multi-engine OCR (Tesseract + EasyOCR) with confidence scoring")
print("✅ Intelligent field mapping using proximity and pattern matching")
print("✅ Data validation with customizable rules")
print("✅ Multiple output formats (JSON, DataFrame, CSV)")
print("✅ Comprehensive error handling and troubleshooting")
print("✅ Visualization of results and analysis")

print("\n🚀 Potential improvements and extensions:")
print("• Add support for handwritten text recognition")
print("• Implement machine learning for better field detection")
print("• Add support for multi-page forms")
print("• Create a web interface for easier usage")
print("• Add batch processing capabilities")
print("• Implement form template learning from examples")
print("• Add support for checkbox and signature detection")

print("\n📁 Generated files:")
if results['success']:
    print("• extracted_form_data.json - JSON format results")
    print("• extracted_form_data.csv - CSV format results")
    print("• ocr_results.png - OCR visualization")
    print("• extraction_analysis.png - Analysis charts")
    if os.path.exists('template_image.ppm'):
        print("• template_image.ppm - Template reference image")

print("\n" + "=" * 60)
print("PIPELINE EXECUTION COMPLETE!")
print("=" * 60)