# Form Data Extraction from Scanned Images

A comprehensive solution for extracting and parsing filled information from scanned form images using OCR and template analysis.

## Features

- **Template Analysis**: Analyzes blank PDF templates to understand form structure and field locations
- **Advanced OCR**: Uses both Tesseract and EasyOCR for robust text recognition
- **Smart Field Mapping**: Maps extracted text to form fields using proximity and pattern matching
- **Data Validation**: Validates extracted data with customizable rules
- **Multiple Output Formats**: Generates results in JSON and pandas DataFrame formats
- **Comprehensive Error Handling**: Provides detailed troubleshooting suggestions
- **Visualization**: Creates visual representations of OCR results and analysis

## Prerequisites

### System Requirements

1. **Python 3.7+**
2. **Tesseract OCR**: Install from [https://github.com/tesseract-ocr/tesseract](https://github.com/tesseract-ocr/tesseract)
   - Windows: Download installer from GitHub releases
   - macOS: `brew install tesseract`
   - Ubuntu/Debian: `sudo apt install tesseract-ocr`
   - CentOS/RHEL: `sudo yum install tesseract`

3. **System Libraries** (for OpenCV):
   - Ubuntu/Debian: `sudo apt-get install libgl1-mesa-glx`
   - CentOS/RHEL: `sudo yum install mesa-libGL`

## Installation

1. **Clone or download this repository**

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify Tesseract installation**:
   ```bash
   tesseract --version
   ```

4. **Start Jupyter Notebook**:
   ```bash
   jupyter notebook
   ```

5. **Open `form_extraction_solution.ipynb`**

## Usage

### Basic Usage

1. Place your PDF template file as `template.pdf`
2. Place your scanned form image as `sample.jpg`
3. Run all cells in the Jupyter notebook

### Custom Files

To use different files, modify the configuration in cell 2:

```python
TEMPLATE_PDF = "your_template.pdf"
SAMPLE_IMAGE = "your_scanned_form.jpg"
```

### Output Formats

The solution generates multiple output formats:

- **JSON**: Structured data with metadata and validation results
- **CSV**: Tabular format for easy analysis
- **Visualizations**: OCR results overlay and analysis charts

## How It Works

### 1. Template Analysis
- Extracts text elements and form fields from PDF template
- Identifies potential field labels using pattern matching
- Creates reference structure for field mapping

### 2. Image Processing
- Preprocesses scanned image (contrast, brightness, noise reduction)
- Applies OCR using both Tesseract and EasyOCR
- Combines results for improved accuracy

### 3. Field Mapping
- Maps OCR results to template fields using proximity analysis
- Groups related text into field values
- Calculates confidence scores for each field

### 4. Data Validation
- Validates extracted data against predefined rules
- Checks format, length, and pattern requirements
- Generates quality scores and error reports

### 5. Output Generation
- Creates structured JSON and DataFrame outputs
- Generates visualizations and analysis charts
- Provides troubleshooting suggestions

## Configuration

### OCR Settings
Modify `TESSERACT_CONFIG` in the notebook to adjust OCR parameters:

```python
TESSERACT_CONFIG = '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz .,()-/'
```

### Image Processing
Adjust `IMAGE_PROCESSING_PARAMS` for different image types:

```python
IMAGE_PROCESSING_PARAMS = {
    'dpi': 300,
    'contrast_factor': 1.2,
    'brightness_factor': 1.1,
    'blur_radius': 0.5,
    'threshold_value': 127
}
```

### Field Detection
Modify `FIELD_DETECTION_PARAMS` for different form layouts:

```python
FIELD_DETECTION_PARAMS = {
    'min_confidence': 30,
    'similarity_threshold': 0.6,
    'proximity_threshold': 50  # pixels
}
```

## Troubleshooting

### Common Issues

1. **"No text detected"**
   - Improve image quality (higher resolution, better contrast)
   - Check if image is properly oriented
   - Try different OCR settings

2. **"No fields could be mapped"**
   - Verify template and filled form have similar layouts
   - Adjust proximity thresholds
   - Check field label patterns

3. **Low confidence scores**
   - Increase image resolution
   - Improve image preprocessing
   - Use cleaner scans

4. **Tesseract not found**
   - Ensure Tesseract is installed and in PATH
   - On Windows, add Tesseract installation directory to PATH

### Performance Tips

- Use high-resolution scans (300 DPI or higher)
- Ensure good contrast between text and background
- Keep forms properly aligned and oriented
- Use clean, noise-free scans

## File Structure

```
├── form_extraction_solution.ipynb  # Main notebook
├── requirements.txt                # Python dependencies
├── README.md                      # This file
├── template.pdf                   # Your PDF template
├── sample.jpg                     # Your scanned form
└── output/                        # Generated files
    ├── extracted_form_data.json
    ├── extracted_form_data.csv
    ├── ocr_results.png
    └── extraction_analysis.png
```

## Supported File Formats

### Input
- **Templates**: PDF files
- **Images**: JPG, PNG, TIFF, BMP

### Output
- **Data**: JSON, CSV
- **Visualizations**: PNG

## License

This project is provided as-is for educational and research purposes.

## Contributing

Feel free to submit issues, feature requests, or improvements to enhance the solution.
